// TeamViewer-style sliding sidebar design
.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #ffffff;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  position: relative;
  overflow: hidden;
}

// Sidebar Background - extends behind main content
.sidebar-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 320px;
  height: 100vh;
  background: #2c3e50;
  z-index: 1;
}

// Top Bar - TeamViewer style
.top-bar {
  background: #ffffff;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  position: relative;
}

.top-bar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  height: 56px;
}

.app-info {
  display: flex;
  align-items: center;
  gap: 12px;

  .menu-toggle-btn {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: transparent;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(0, 0, 0, 0.05);
    }

    mat-icon {
      color: #666;
      font-size: 20px;
    }
  }

  .app-icon {
    color: #2196F3;
    font-size: 24px;
    width: 24px;
    height: 24px;
  }

  .app-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }
}

.loan-info {
  .loan-badge {
    display: flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(145deg, #e3f2fd, #bbdefb);
    padding: 8px 16px;
    border-radius: 20px;
    box-shadow: 
      inset 2px 2px 4px rgba(0, 0, 0, 0.1),
      inset -2px -2px 4px rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(33, 150, 243, 0.2);
    
    mat-icon {
      color: #1976D2;
      font-size: 18px;
      width: 18px;
      height: 18px;
    }
    
    .loan-number {
      font-weight: 600;
      color: #1976D2;
      font-size: 14px;
    }
  }
}

.top-actions {
  display: flex;
  gap: 4px;
  
  .action-btn {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
    box-shadow: 
      2px 2px 4px rgba(0, 0, 0, 0.1),
      inset 1px 1px 2px rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
    
    &:hover {
      background: linear-gradient(145deg, #e9ecef, #dee2e6);
      box-shadow: 
        1px 1px 2px rgba(0, 0, 0, 0.15),
        inset 2px 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    mat-icon {
      color: #666;
      font-size: 20px;
    }
    
    &.account-btn {
      .user-avatar {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background: linear-gradient(145deg, #2196F3, #1976D2);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: 600;
      }
    }
  }
}

.app-content {
  display: flex;
  flex: 1;
  overflow: hidden;
  position: relative;
  z-index: 2;
}

// Compact Sidebar - Always visible icons
.sidebar-compact {
  width: 60px;
  background: #2c3e50;
  display: flex;
  flex-direction: column;
  z-index: 3;
  position: relative;
}

.sidebar-icons {
  display: flex;
  flex-direction: column;
  padding: 16px 0;
  gap: 8px;
}

.sidebar-icon-btn {
  width: 44px;
  height: 44px;
  margin: 0 8px;
  border: none;
  border-radius: 8px;
  background: transparent;
  color: #bdc3c7;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #ecf0f1;
  }

  &.active {
    background: #3498db;
    color: #ffffff;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
  }

  mat-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
  }
}

.sidebar-divider {
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
  margin: 8px 16px;
}

// Expandable Sidebar Panel - Slides over main content
.sidebar-expanded {
  position: fixed;
  top: 0;
  left: 60px;
  width: 260px;
  height: 100vh;
  background: #ffffff;
  box-shadow: 2px 0 16px rgba(0, 0, 0, 0.15);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  z-index: 4;

  &.visible {
    transform: translateX(0);
  }
}

.sidebar-panel {
  height: 100%;
  overflow-y: auto;
}

.sidebar-header {
  padding: 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: space-between;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }

  .close-btn {
    width: 32px;
    height: 32px;

    mat-icon {
      font-size: 18px;
      color: #666;
    }
  }
}

.sidebar-nav {
  padding: 16px;
}

.nav-section {
  margin-bottom: 16px;
}

.nav-item {
  margin-bottom: 8px;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
  cursor: pointer;

  &:hover {
    .nav-item-content {
      background: #f8f9fa;
    }
  }

  &.active {
    .nav-item-content {
      background: #e3f2fd;
      border-left: 4px solid #2196F3;

      mat-icon {
        color: #2196F3;
      }

      span {
        color: #2196F3;
        font-weight: 600;
      }

      .nav-description {
        color: #1976D2;
      }
    }
  }
}

.nav-item-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  padding: 16px;
  background: #ffffff;
  transition: all 0.2s ease;

  mat-icon {
    color: #666;
    font-size: 20px;
    width: 20px;
    height: 20px;
    margin-bottom: 4px;
  }

  span {
    font-size: 14px;
    font-weight: 500;
    color: #333;
  }

  .nav-description {
    font-size: 12px;
    color: #999;
    font-weight: 400;
    line-height: 1.3;
  }
}

.nav-divider {
  height: 1px;
  background: #e0e0e0;
  margin: 16px 0;
}

// Main Content Area - Slides right when sidebar expands
.main-content {
  flex: 1;
  overflow-y: auto;
  background: #ffffff;
  padding: 20px;
  transition: transform 0.3s ease;
  position: relative;
  z-index: 2;

  &.shifted {
    transform: translateX(260px);
  }
}

// Footer Bar - TeamViewer style
.footer-bar {
  background: #ffffff;
  border-top: 1px solid #e0e0e0;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  position: relative;
}

.footer-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  height: 40px;

  .status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #4caf50;
    font-weight: 500;

    &::before {
      content: '';
      width: 8px;
      height: 8px;
      background: #4caf50;
      border-radius: 50%;
      box-shadow: 0 0 4px rgba(76, 175, 80, 0.5);
    }
  }

  .copyright {
    font-size: 12px;
    color: #666;
  }
}

// Account Dropdown Styling
.account-dropdown {
  .account-header {
    padding: 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    gap: 12px;

    .user-avatar-large {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      background: linear-gradient(145deg, #2196F3, #1976D2);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      font-weight: 600;
      box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
    }

    .user-details {
      .user-name-large {
        font-weight: 600;
        color: #333;
        margin-bottom: 2px;
      }

      .user-email {
        font-size: 12px;
        color: #666;
        margin-bottom: 4px;
      }

      .user-role-badge {
        background: #e3f2fd;
        color: #1976D2;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 10px;
        font-weight: 600;
        display: inline-block;
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .sidebar-background {
    width: 280px;
  }

  .sidebar-expanded {
    width: 220px;
  }

  .main-content.shifted {
    transform: translateX(220px);
  }

  .top-bar-content {
    padding: 8px 16px;

    .app-title {
      font-size: 16px;
    }

    .loan-badge {
      padding: 6px 12px;

      .loan-number {
        font-size: 12px;
      }
    }
  }

  .nav-item-content {
    padding: 12px;

    span {
      font-size: 13px;
    }

    .nav-description {
      font-size: 11px;
    }
  }
}
