// Google-style simple layout
.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  background: #ffffff;
  font-family: 'Google Sans', 'Roboto', sans-serif;
  position: relative;
  overflow: hidden;
}

// Simple Header
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background: #ffffff;
  border-bottom: 1px solid #e8eaed;
  z-index: 1000;
  position: relative;
}

.menu-btn {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(60, 64, 67, 0.08);
  }

  mat-icon {
    color: #5f6368;
    font-size: 24px;
  }
}

.loan-display {
  font-size: 16px;
  color: #202124;
  font-weight: 400;
}

.user-menu {
  .user-btn {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(60, 64, 67, 0.08);
    }
  }

  .user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #1a73e8;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 500;
  }
}

// Hidden Left Sidebar
.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  width: 280px;
  height: 100vh;
  background: #ffffff;
  border-right: 1px solid #e8eaed;
  transform: translateX(-100%);
  transition: transform 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  z-index: 999;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

  &.open {
    transform: translateX(0);
  }
}

.sidebar-content {
  padding: 80px 0 24px 0; // Top padding to account for header
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 24px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  color: #5f6368;

  &:hover {
    background-color: rgba(60, 64, 67, 0.08);
  }

  mat-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
  }

  span {
    font-size: 14px;
    font-weight: 400;
  }
}

// Main Content - Slides right when menu opens
.main-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  background: #ffffff;
  transition: transform 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  position: relative;
  z-index: 1;
  min-height: 0;

  &.shifted {
    transform: translateX(280px);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .sidebar {
    width: 240px;
  }

  .main-content.shifted {
    transform: translateX(240px);
  }

  .header {
    padding: 12px 16px;
  }

  .loan-display {
    font-size: 14px;
  }
}
