<div class="app-container">
  <!-- Left Menu - Full viewport height -->
  <nav class="sidebar" [class.visible]="sideMenuOpen">
    <div class="sidebar-content">
      <div class="nav-item" (click)="navigateToHome(); closeSideMenu()">
        <mat-icon>home</mat-icon>
        <span>Home</span>
      </div>

      <div class="nav-item" (click)="navigateToDashboard(); closeSideMenu()">
        <mat-icon>dashboard</mat-icon>
        <span>Dashboard</span>
      </div>

      <div class="nav-item" (click)="navigateToReports(); closeSideMenu()">
        <mat-icon>assessment</mat-icon>
        <span>Reports</span>
      </div>

      <div class="nav-item" (click)="navigateToAnalytics(); closeSideMenu()">
        <mat-icon>analytics</mat-icon>
        <span>Analytics</span>
      </div>

      <div class="nav-item" (click)="navigateToSettings(); closeSideMenu()">
        <mat-icon>settings</mat-icon>
        <span>Settings</span>
      </div>

      <div class="nav-item" (click)="navigateToHelp(); closeSideMenu()">
        <mat-icon>help</mat-icon>
        <span>Help</span>
      </div>
    </div>
  </nav>

  <!-- Main Content with Header Component -->
  <main class="main-content" [class.shifted]="sideMenuOpen">
    <!-- Header Component at top of content -->
    <header class="content-header">
      <button
        mat-icon-button
        class="menu-btn"
        (click)="sideMenuOpen ? closeSideMenu() : openSideMenu()"
      >
        <mat-icon>menu</mat-icon>
      </button>

      <div class="loan-display">
        Loan: {{ activeLoanNumber }}
      </div>

      <div class="user-menu">
        <button
          mat-icon-button
          [matMenuTriggerFor]="accountMenu"
          class="user-btn"
        >
          <div class="user-avatar">{{ user.avatar }}</div>
        </button>

        <mat-menu #accountMenu="matMenu">
          <button mat-menu-item (click)="openProfile()">
            <mat-icon>person</mat-icon>
            <span>Profile</span>
          </button>
          <button mat-menu-item (click)="openSettings()">
            <mat-icon>settings</mat-icon>
            <span>Settings</span>
          </button>
          <mat-divider></mat-divider>
          <button mat-menu-item (click)="logout()">
            <mat-icon>logout</mat-icon>
            <span>Sign Out</span>
          </button>
        </mat-menu>
      </div>
    </header>

    <!-- Page Content -->
    <div class="page-content">
      <ng-content></ng-content>
    </div>
  </main>
</div>
