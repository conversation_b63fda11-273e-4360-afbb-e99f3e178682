<div class="app-container">
  <!-- Inset-Style Top Bar -->
  <div class="top-bar">
    <div class="top-bar-content">
      <div class="app-info">
        <button
          mat-icon-button
          class="menu-toggle-btn"
          (click)="sideMenuOpen ? closeSideMenu() : openSideMenu()"
        >
          <mat-icon>{{ sideMenuOpen ? 'menu_open' : 'menu' }}</mat-icon>
        </button>
        <mat-icon class="app-icon">account_balance</mat-icon>
        <span class="app-title">{{ appTitle }}</span>
      </div>
      
      <div class="loan-info">
        <div class="loan-badge">
          <mat-icon>description</mat-icon>
          <span class="loan-number">Loan: {{ activeLoanNumber }}</span>
        </div>
      </div>
      
      <div class="top-actions">
        <button mat-icon-button class="action-btn">
          <mat-icon>search</mat-icon>
        </button>
        <button mat-icon-button class="action-btn">
          <mat-icon>notifications</mat-icon>
        </button>
        
        <!-- Account Menu -->
        <button
          mat-icon-button
          [matMenuTriggerFor]="accountMenu"
          class="action-btn account-btn"
        >
          <div class="user-avatar">{{ user.avatar }}</div>
        </button>

        <mat-menu #accountMenu="matMenu" class="account-dropdown">
          <div class="account-header">
            <div class="user-avatar-large">{{ user.avatar }}</div>
            <div class="user-details">
              <div class="user-name-large">{{ user.name }}</div>
              <div class="user-email">{{ user.email }}</div>
              <div class="user-role-badge">{{ user.role }}</div>
            </div>
          </div>

          <mat-divider></mat-divider>

          <button mat-menu-item (click)="openProfile()">
            <mat-icon>person</mat-icon>
            <span>Profile</span>
          </button>

          <button mat-menu-item (click)="openSettings()">
            <mat-icon>settings</mat-icon>
            <span>Settings</span>
          </button>

          <mat-divider></mat-divider>

          <button mat-menu-item (click)="logout()">
            <mat-icon>logout</mat-icon>
            <span>Sign Out</span>
          </button>
        </mat-menu>
      </div>
    </div>
  </div>

  <!-- TeamViewer-style Sidebar Background -->
  <div class="sidebar-background"></div>

  <div class="app-content">
    <!-- Compact Sidebar Icons -->
    <div class="sidebar-compact">
      <div class="sidebar-icons">
        <button
          class="sidebar-icon-btn active"
          (click)="navigateToHome()"
          title="Home"
        >
          <mat-icon>home</mat-icon>
        </button>

        <button
          class="sidebar-icon-btn"
          (click)="navigateToDashboard()"
          title="Dashboard"
        >
          <mat-icon>dashboard</mat-icon>
        </button>

        <div class="sidebar-divider"></div>

        <button
          class="sidebar-icon-btn"
          (click)="navigateToReports()"
          title="Reports"
        >
          <mat-icon>assessment</mat-icon>
        </button>

        <button
          class="sidebar-icon-btn"
          (click)="navigateToAnalytics()"
          title="Analytics"
        >
          <mat-icon>analytics</mat-icon>
        </button>

        <div class="sidebar-divider"></div>

        <button
          class="sidebar-icon-btn"
          (click)="navigateToSettings()"
          title="Settings"
        >
          <mat-icon>settings</mat-icon>
        </button>

        <button
          class="sidebar-icon-btn"
          (click)="navigateToHelp()"
          title="Help"
        >
          <mat-icon>help</mat-icon>
        </button>
      </div>
    </div>

    <!-- Expandable Sidebar Panel -->
    <div class="sidebar-expanded" [class.visible]="sideMenuOpen">
      <div class="sidebar-panel">
        <div class="sidebar-header">
          <h3>Navigation</h3>
          <button
            mat-icon-button
            class="close-btn"
            (click)="closeSideMenu()"
          >
            <mat-icon>close</mat-icon>
          </button>
        </div>

        <div class="sidebar-nav">
          <div class="nav-section">
            <div class="nav-item active" (click)="navigateToHome()">
              <div class="nav-item-content">
                <mat-icon>home</mat-icon>
                <span>Home</span>
                <span class="nav-description">Main dashboard and overview</span>
              </div>
            </div>

            <div class="nav-item" (click)="navigateToDashboard()">
              <div class="nav-item-content">
                <mat-icon>dashboard</mat-icon>
                <span>Dashboard</span>
                <span class="nav-description">Analytics and metrics</span>
              </div>
            </div>
          </div>

          <div class="nav-divider"></div>

          <div class="nav-section">
            <div class="nav-item" (click)="navigateToReports()">
              <div class="nav-item-content">
                <mat-icon>assessment</mat-icon>
                <span>Reports</span>
                <span class="nav-description">Generate and view reports</span>
              </div>
            </div>

            <div class="nav-item" (click)="navigateToAnalytics()">
              <div class="nav-item-content">
                <mat-icon>analytics</mat-icon>
                <span>Analytics</span>
                <span class="nav-description">Data insights and trends</span>
              </div>
            </div>
          </div>

          <div class="nav-divider"></div>

          <div class="nav-section">
            <div class="nav-item" (click)="navigateToSettings()">
              <div class="nav-item-content">
                <mat-icon>settings</mat-icon>
                <span>Settings</span>
                <span class="nav-description">Application preferences</span>
              </div>
            </div>

            <div class="nav-item" (click)="navigateToHelp()">
              <div class="nav-item-content">
                <mat-icon>help</mat-icon>
                <span>Help</span>
                <span class="nav-description">Documentation and support</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content with Sliding Effect -->
    <div class="main-content" [class.shifted]="sideMenuOpen">
      <ng-content></ng-content>
    </div>
  </div>

  <!-- Footer -->
  <div class="footer-bar">
    <div class="footer-content">
      <span class="status-indicator">Ready to connect</span>
      <span class="copyright">© 2024 Rocket Logic Ensemble. All rights reserved.</span>
    </div>
  </div>
</div>
