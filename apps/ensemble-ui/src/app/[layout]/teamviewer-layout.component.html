<div class="app-container">
  <!-- TeamViewer-style Top Bar -->
  <div class="top-bar">
    <div class="top-bar-content">
      <div class="app-info">
        <mat-icon class="app-icon">account_balance</mat-icon>
        <span class="app-title">{{ appTitle }}</span>
      </div>
      
      <div class="loan-info">
        <div class="loan-badge">
          <mat-icon>description</mat-icon>
          <span class="loan-number">Loan: {{ activeLoanNumber }}</span>
        </div>
      </div>
      
      <div class="top-actions">
        <button mat-icon-button class="action-btn">
          <mat-icon>search</mat-icon>
        </button>
        <button mat-icon-button class="action-btn">
          <mat-icon>notifications</mat-icon>
        </button>
        
        <!-- Account Menu -->
        <button
          mat-icon-button
          [matMenuTriggerFor]="accountMenu"
          class="action-btn account-btn"
        >
          <div class="user-avatar">{{ user.avatar }}</div>
        </button>

        <mat-menu #accountMenu="matMenu" class="account-dropdown">
          <div class="account-header">
            <div class="user-avatar-large">{{ user.avatar }}</div>
            <div class="user-details">
              <div class="user-name-large">{{ user.name }}</div>
              <div class="user-email">{{ user.email }}</div>
              <div class="user-role-badge">{{ user.role }}</div>
            </div>
          </div>

          <mat-divider></mat-divider>

          <button mat-menu-item (click)="openProfile()">
            <mat-icon>person</mat-icon>
            <span>Profile</span>
          </button>

          <button mat-menu-item (click)="openSettings()">
            <mat-icon>settings</mat-icon>
            <span>Settings</span>
          </button>

          <mat-divider></mat-divider>

          <button mat-menu-item (click)="logout()">
            <mat-icon>logout</mat-icon>
            <span>Sign Out</span>
          </button>
        </mat-menu>
      </div>
    </div>
  </div>

  <div class="app-content">
    <!-- TeamViewer-style Left Sidebar -->
    <div class="sidebar-container">
      <div class="sidebar-panel">
        <div class="sidebar-header">
          <h3>Navigation</h3>
        </div>
        
        <div class="sidebar-nav">
          <div class="nav-section">
            <div class="nav-item active" (click)="navigateToHome()">
              <div class="nav-item-content">
                <mat-icon>home</mat-icon>
                <span>Home</span>
              </div>
            </div>
            
            <div class="nav-item" (click)="navigateToDashboard()">
              <div class="nav-item-content">
                <mat-icon>dashboard</mat-icon>
                <span>Dashboard</span>
              </div>
            </div>
          </div>
          
          <div class="nav-divider"></div>
          
          <div class="nav-section">
            <div class="nav-item" (click)="navigateToReports()">
              <div class="nav-item-content">
                <mat-icon>assessment</mat-icon>
                <span>Reports</span>
              </div>
            </div>
            
            <div class="nav-item" (click)="navigateToAnalytics()">
              <div class="nav-item-content">
                <mat-icon>analytics</mat-icon>
                <span>Analytics</span>
              </div>
            </div>
          </div>
          
          <div class="nav-divider"></div>
          
          <div class="nav-section">
            <div class="nav-item" (click)="navigateToSettings()">
              <div class="nav-item-content">
                <mat-icon>settings</mat-icon>
                <span>Settings</span>
              </div>
            </div>
            
            <div class="nav-item" (click)="navigateToHelp()">
              <div class="nav-item-content">
                <mat-icon>help</mat-icon>
                <span>Help</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
      <ng-content></ng-content>
    </div>
  </div>

  <!-- Footer -->
  <div class="footer-bar">
    <div class="footer-content">
      <span class="status-indicator">Ready to connect</span>
      <span class="copyright">© 2024 Rocket Logic Ensemble. All rights reserved.</span>
    </div>
  </div>
</div>
