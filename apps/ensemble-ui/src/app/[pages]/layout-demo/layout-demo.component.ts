import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { LayoutComponent } from '../../[layout]/layout.component';
import { TeamViewerLayoutComponent } from '../../[layout]/teamviewer-layout.component';

@Component({
  selector: 'ens-layout-demo',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    LayoutComponent,
    TeamViewerLayoutComponent
  ],
  templateUrl: './layout-demo.component.html',
  styleUrl: './layout-demo.component.scss'
})
export class LayoutDemoComponent {
  currentLayout: 'default' | 'teamviewer' = 'teamviewer';

  switchToDefault() {
    this.currentLayout = 'default';
  }

  switchToTeamViewer() {
    this.currentLayout = 'teamviewer';
  }
}
