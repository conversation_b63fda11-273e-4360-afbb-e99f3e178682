.layout-demo-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-switcher {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  
  .switcher-card {
    padding: 16px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    
    h2 {
      margin: 0 0 8px 0;
      font-size: 18px;
      color: #333;
    }
    
    p {
      margin: 0 0 16px 0;
      font-size: 14px;
      color: #666;
    }
  }
  
  .layout-buttons {
    display: flex;
    gap: 8px;
    
    button {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 12px;
      padding: 8px 16px;
      
      mat-icon {
        font-size: 16px;
        width: 16px;
        height: 16px;
      }
    }
  }
}

.layout-display {
  flex: 1;
  overflow: hidden;
}

.demo-content {
  padding: 20px;
  
  .content-card {
    max-width: 800px;
    margin: 0 auto;
    padding: 24px;
    
    h3 {
      margin: 0 0 16px 0;
      color: #333;
      font-size: 24px;
    }
    
    p {
      margin: 0 0 20px 0;
      color: #666;
      line-height: 1.6;
    }
    
    .feature-list {
      margin-bottom: 24px;
      
      h4 {
        margin: 0 0 12px 0;
        color: #333;
        font-size: 16px;
      }
      
      ul {
        margin: 0;
        padding-left: 20px;
        
        li {
          margin-bottom: 8px;
          color: #666;
          line-height: 1.5;
        }
      }
    }
    
    .demo-actions {
      background: linear-gradient(145deg, #f8f9fa, #e9ecef);
      padding: 16px;
      border-radius: 8px;
      border: 1px solid rgba(0, 0, 0, 0.08);
      
      h4 {
        margin: 0 0 8px 0;
        color: #333;
        font-size: 14px;
      }
      
      p {
        margin: 0;
        font-size: 13px;
        color: #666;
      }
    }
    
    &.teamviewer-content {
      background: linear-gradient(145deg, #ffffff, #f8f9fa);
      border-radius: 16px;
      box-shadow: 
        0 4px 12px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
      border: 1px solid rgba(0, 0, 0, 0.08);
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .layout-switcher {
    position: relative;
    top: auto;
    right: auto;
    margin: 16px;
    
    .layout-buttons {
      flex-direction: column;
      
      button {
        width: 100%;
        justify-content: center;
      }
    }
  }
  
  .demo-content {
    padding: 16px;
    
    .content-card {
      padding: 16px;
      
      h3 {
        font-size: 20px;
      }
    }
  }
}
